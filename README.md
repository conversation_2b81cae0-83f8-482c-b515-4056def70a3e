# BookFinder - Advanced Book Discovery App

A comprehensive React-based book discovery application designed for college students and book enthusiasts. Built with modern web technologies and powered by the Open Library API.

## 🚀 Features

### 📚 Advanced Search Capabilities
- **Multi-field Search**: Search by title, author, subject, ISBN, publisher, or general query
- **Smart Filters**: Filter by language, publication year range, availability, and more
- **Popular Subjects**: Quick access to common academic and literary genres
- **Search History**: Keep track of recent searches for easy re-access

### ❤️ Personal Library Management
- **Favorites System**: Save books you love with personal notes
- **Custom Reading Lists**: Create and organize multiple reading lists for different purposes
- **Study Tools**: Rate books, track reading progress, and add study notes

### 🎓 Student-Focused Features
- **Academic Citations**: Auto-generate properly formatted citations
- **Reading Progress Tracking**: Monitor your progress through books with page counters
- **Study Notes**: Add personal insights, quotes, and thoughts about each book
- **Rating System**: Rate books on a 5-star scale for future reference

### 📱 Modern User Experience
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Intuitive Navigation**: Clean, modern interface with easy-to-use navigation
- **Real-time Search**: Fast, responsive search with loading states and error handling
- **Persistent Data**: All your data is saved locally and persists between sessions

## 🛠️ Technology Stack

- **Frontend**: React 19 with Hooks
- **Styling**: Tailwind CSS 4 for modern, responsive design
- **Routing**: React Router DOM for seamless navigation
- **Icons**: Lucide React for beautiful, consistent icons
- **Build Tool**: Vite for fast development and optimized builds
- **API**: Open Library Search API for comprehensive book data

## 🏃‍♂️ Getting Started

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd react-book-finder
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` (or the port shown in your terminal)

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint for code quality

## 📖 How to Use

### Searching for Books
1. Use the main search interface on the homepage
2. Enter your search terms in the general search box, or expand "Advanced" for specific field searches
3. Use popular subject tags for quick searches
4. Apply filters for language, publication year, and availability
5. Sort results by relevance, date, title, or author

### Managing Your Library
1. **Add to Favorites**: Click the heart icon on any book card
2. **Create Reading Lists**: Go to "Reading Lists" and create custom lists for different purposes
3. **Add Books to Lists**: Use the "Add to List" button on book cards
4. **View Details**: Click on book titles to see detailed information

### Study Tools
1. **Rate Books**: Use the star rating system on book detail pages
2. **Track Progress**: Set your current page and total pages to monitor reading progress
3. **Add Notes**: Write study notes, insights, or quotes for future reference
4. **Generate Citations**: Copy properly formatted academic citations

## 🎯 User Persona: Alex the College Student

This app is designed specifically for college students like Alex who need to:
- Research books for academic projects and papers
- Discover new books in their field of study
- Keep track of required and recommended reading
- Organize books by course or topic
- Take notes and track progress for better study habits
- Generate citations for academic work

## 🌟 Key Benefits

- **Comprehensive Search**: Access millions of books from Open Library
- **Academic Focus**: Features designed specifically for students and researchers
- **Organization Tools**: Keep your reading organized with lists and favorites
- **Progress Tracking**: Monitor your reading goals and progress
- **Citation Support**: Generate proper academic citations instantly
- **Mobile-Friendly**: Study and search on any device

## 🔧 Technical Features

- **Performance Optimized**: Fast loading with efficient API calls and caching
- **Error Handling**: Graceful error handling with user-friendly messages
- **Accessibility**: Keyboard navigation and screen reader friendly
- **Local Storage**: All user data persists locally for privacy and offline access
- **Responsive Images**: Optimized book cover loading with fallbacks

## 🤝 Contributing

This project is open for contributions! Feel free to:
- Report bugs or suggest features
- Improve the user interface or user experience
- Add new functionality
- Optimize performance
- Improve documentation

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **Open Library**: For providing the comprehensive book database API
- **Tailwind CSS**: For the beautiful, responsive design system
- **Lucide**: For the clean, consistent icon set
- **React Team**: For the amazing React framework
