import React from 'react'
import { Clock, Search } from 'lucide-react'

const SearchHistory = ({ history, onHistorySearch }) => {
  if (!history || history.length === 0) {
    return null
  }

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now - date) / (1000 * 60 * 60)
    
    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hour${Math.floor(diffInHours) !== 1 ? 's' : ''} ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const getSearchTypeLabel = (type) => {
    const labels = {
      title: 'Title',
      author: 'Author',
      subject: 'Subject',
      isbn: 'ISBN'
    }
    return labels[type] || 'Title'
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center gap-2 mb-4">
        <Clock className="h-5 w-5 text-gray-500" />
        <h3 className="text-lg font-semibold text-gray-900">Recent Searches</h3>
      </div>
      
      <div className="space-y-2">
        {history.slice(0, 5).map((item, index) => (
          <button
            key={index}
            onClick={() => onHistorySearch(item)}
            className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-colors group"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Search className="h-4 w-4 text-gray-400 group-hover:text-blue-500" />
                <div>
                  <p className="font-medium text-gray-900 group-hover:text-blue-600">
                    {item.term}
                  </p>
                  <p className="text-sm text-gray-500">
                    Search by {getSearchTypeLabel(item.type)}
                  </p>
                </div>
              </div>
              <span className="text-xs text-gray-400">
                {formatTimestamp(item.timestamp)}
              </span>
            </div>
          </button>
        ))}
      </div>
      
      {history.length > 5 && (
        <p className="text-sm text-gray-500 mt-3 text-center">
          Showing 5 of {history.length} recent searches
        </p>
      )}
    </div>
  )
}

export default SearchHistory
