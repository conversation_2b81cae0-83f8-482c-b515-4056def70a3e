import React, { useState, useEffect } from 'react'
import SearchForm from './SearchForm'
import BookGrid from './BookGrid'
import SearchHistory from './SearchHistory'
import LoadingSpinner from './LoadingSpinner'
import { searchBooks } from '../services/openLibraryAPI'

const SearchPage = ({ favorites, addToFavorites, searchHistory, addToSearchHistory }) => {
  const [books, setBooks] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [searchPerformed, setSearchPerformed] = useState(false)
  const [currentSearch, setCurrentSearch] = useState('')
  const [totalResults, setTotalResults] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)

  const handleSearch = async (searchParams, page = 1) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await searchBooks(searchParams, page)
      
      if (page === 1) {
        setBooks(response.docs)
        setCurrentSearch(searchParams.query)
        setSearchPerformed(true)
        addToSearchHistory(searchParams.query, searchParams.type)
      } else {
        setBooks(prev => [...prev, ...response.docs])
      }
      
      setTotalResults(response.numFound)
      setCurrentPage(page)
      setHasMore(response.docs.length === 20 && (page * 20) < response.numFound)
      
    } catch (err) {
      setError('Failed to search books. Please try again.')
      console.error('Search error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const searchParams = {
        query: currentSearch,
        type: 'title' // You might want to store the search type as well
      }
      handleSearch(searchParams, currentPage + 1)
    }
  }

  const handleHistorySearch = (historyItem) => {
    const searchParams = {
      query: historyItem.term,
      type: historyItem.type
    }
    handleSearch(searchParams)
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Welcome Section */}
      {!searchPerformed && (
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to BookFinder, Alex! 📚
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Discover your next favorite book with our comprehensive search
          </p>
        </div>
      )}

      {/* Search Form */}
      <div className="mb-8">
        <SearchForm onSearch={handleSearch} loading={loading} />
      </div>

      {/* Search History */}
      {searchHistory.length > 0 && !searchPerformed && (
        <div className="mb-8">
          <SearchHistory 
            history={searchHistory} 
            onHistorySearch={handleHistorySearch}
          />
        </div>
      )}

      {/* Loading State */}
      {loading && currentPage === 1 && (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Search Results */}
      {searchPerformed && !loading && books.length > 0 && (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-2xl font-semibold text-gray-900">
              Search Results for "{currentSearch}"
            </h3>
            <p className="text-gray-600">
              {totalResults.toLocaleString()} books found
            </p>
          </div>
          
          <BookGrid 
            books={books} 
            favorites={favorites}
            addToFavorites={addToFavorites}
          />
          
          {/* Load More Button */}
          {hasMore && (
            <div className="text-center mt-8">
              <button
                onClick={handleLoadMore}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Loading...' : 'Load More Books'}
              </button>
            </div>
          )}
        </div>
      )}

      {/* No Results */}
      {searchPerformed && !loading && books.length === 0 && (
        <div className="text-center py-12">
          <p className="text-xl text-gray-600 mb-4">
            No books found for "{currentSearch}"
          </p>
          <p className="text-gray-500">
            Try adjusting your search terms or search by author instead
          </p>
        </div>
      )}
    </div>
  )
}

export default SearchPage
