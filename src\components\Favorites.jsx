import React, { useState } from 'react'
import { Heart, Search, BookOpen, Trash2 } from 'lucide-react'
import BookGrid from './BookGrid'

const Favorites = ({ favorites, addToFavorites }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('dateAdded') // dateAdded, title, author, year

  // Filter favorites based on search term
  const filteredFavorites = favorites.filter(book => {
    if (!searchTerm) return true
    
    const searchLower = searchTerm.toLowerCase()
    const titleMatch = book.title?.toLowerCase().includes(searchLower)
    const authorMatch = book.author_name?.some(author => 
      author.toLowerCase().includes(searchLower)
    )
    
    return titleMatch || authorMatch
  })

  // Sort favorites
  const sortedFavorites = [...filteredFavorites].sort((a, b) => {
    switch (sortBy) {
      case 'title':
        return (a.title || '').localeCompare(b.title || '')
      case 'author':
        const authorA = a.author_name?.[0] || ''
        const authorB = b.author_name?.[0] || ''
        return authorA.localeCompare(authorB)
      case 'year':
        return (b.first_publish_year || 0) - (a.first_publish_year || 0)
      case 'dateAdded':
      default:
        // Since we don't track when favorites were added, we'll use the order they appear
        return 0
    }
  })

  const clearAllFavorites = () => {
    if (window.confirm('Are you sure you want to remove all favorites? This action cannot be undone.')) {
      // Remove all favorites by calling addToFavorites for each one
      favorites.forEach(book => addToFavorites(book))
    }
  }

  if (favorites.length === 0) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
          <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            No Favorites Yet
          </h2>
          <p className="text-gray-600 mb-6">
            Start building your reading list by adding books to your favorites!
          </p>
          <p className="text-sm text-gray-500">
            Click the heart icon on any book to add it to your favorites.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Heart className="h-8 w-8 text-red-500 fill-current" />
          <h1 className="text-3xl font-bold text-gray-900">
            Your Favorites
          </h1>
          <span className="bg-red-100 text-red-800 text-sm px-3 py-1 rounded-full">
            {favorites.length} book{favorites.length !== 1 ? 's' : ''}
          </span>
        </div>
        <p className="text-gray-600">
          Your personal collection of books to read and remember
        </p>
      </div>

      {/* Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search your favorites..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex items-center gap-4">
            {/* Sort */}
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="dateAdded">Date Added</option>
                <option value="title">Title</option>
                <option value="author">Author</option>
                <option value="year">Publication Year</option>
              </select>
            </div>

            {/* Clear All */}
            <button
              onClick={clearAllFavorites}
              className="flex items-center gap-2 text-red-600 hover:text-red-700 px-3 py-2 rounded-lg hover:bg-red-50 transition-colors"
              title="Clear all favorites"
            >
              <Trash2 className="h-4 w-4" />
              <span className="hidden sm:inline">Clear All</span>
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      {filteredFavorites.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <BookOpen className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No favorites match your search
          </h3>
          <p className="text-gray-600">
            Try adjusting your search terms or browse all your favorites
          </p>
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="mt-4 text-blue-600 hover:text-blue-700 font-medium"
            >
              Clear search
            </button>
          )}
        </div>
      ) : (
        <>
          {/* Results count */}
          {searchTerm && (
            <div className="mb-4">
              <p className="text-gray-600">
                Showing {filteredFavorites.length} of {favorites.length} favorites
                {searchTerm && ` for "${searchTerm}"`}
              </p>
            </div>
          )}

          {/* Books Grid */}
          <BookGrid 
            books={sortedFavorites}
            favorites={favorites}
            addToFavorites={addToFavorites}
          />
        </>
      )}
    </div>
  )
}

export default Favorites
