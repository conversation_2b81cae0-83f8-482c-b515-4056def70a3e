import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { Heart, Calendar, User, BookOpen, Download } from 'lucide-react'

const BookCard = ({ book, isFavorite, onToggleFavorite }) => {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  const bookKey = book.key.replace('/works/', '')

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
      {/* Book Cover */}
      <div className="relative aspect-[3/4] bg-gray-100">
        <Link to={`/book/${bookKey}`} className="block w-full h-full">
          {!imageError && book.coverUrl ? (
            <>
              {imageLoading && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}
              <img
                src={book.coverUrl}
                alt={`Cover of ${book.title}`}
                className={`w-full h-full object-cover transition-opacity duration-200 ${
                  imageLoading ? 'opacity-0' : 'opacity-100'
                }`}
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200">
              <BookOpen className="h-12 w-12 text-blue-400" />
            </div>
          )}
        </Link>
        
        {/* Favorite Button */}
        <button
          onClick={onToggleFavorite}
          className={`absolute top-2 right-2 p-2 rounded-full transition-colors ${
            isFavorite 
              ? 'bg-red-500 text-white hover:bg-red-600' 
              : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500'
          }`}
          title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
        >
          <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
        </button>

        {/* E-book Badge */}
        {book.hasEbook && (
          <div className="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
            <Download className="h-3 w-3" />
            E-book
          </div>
        )}
      </div>

      {/* Book Info */}
      <div className="p-4">
        <Link to={`/book/${bookKey}`} className="block">
          <h3 className="font-semibold text-gray-900 line-clamp-2 hover:text-blue-600 transition-colors mb-2">
            {book.title}
          </h3>
        </Link>
        
        {/* Authors */}
        {book.authors && book.authors.length > 0 && (
          <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
            <User className="h-3 w-3" />
            <span className="line-clamp-1">
              {book.authors.slice(0, 2).join(', ')}
              {book.authors.length > 2 && ` +${book.authors.length - 2} more`}
            </span>
          </div>
        )}

        {/* Publication Year */}
        {book.publishYear && (
          <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
            <Calendar className="h-3 w-3" />
            <span>{book.publishYear}</span>
          </div>
        )}

        {/* Edition Count */}
        {book.edition_count && (
          <div className="text-xs text-gray-500">
            {book.edition_count} edition{book.edition_count !== 1 ? 's' : ''}
          </div>
        )}

        {/* Subjects Preview */}
        {book.subjects && book.subjects.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-1">
            {book.subjects.slice(0, 2).map((subject, index) => (
              <span
                key={index}
                className="inline-block bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full"
              >
                {subject}
              </span>
            ))}
            {book.subjects.length > 2 && (
              <span className="text-xs text-gray-500">
                +{book.subjects.length - 2} more
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default BookCard
