import React from 'react'
import BookCard from './BookCard'

const BookGrid = ({ books, favorites, addToFavorites }) => {
  if (!books || books.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No books to display</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      {books.map((book, index) => (
        <BookCard
          key={`${book.key}-${index}`}
          book={book}
          isFavorite={favorites.some(fav => fav.key === book.key)}
          onToggleFavorite={() => addToFavorites(book)}
        />
      ))}
    </div>
  )
}

export default BookGrid
