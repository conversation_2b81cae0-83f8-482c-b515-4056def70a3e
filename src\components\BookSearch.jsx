import { useState } from 'react'
import { searchBooks } from '../services/openLibraryAPI'

const BookSearch = () => {
  const [query, setQuery] = useState('')
  const [searchType, setSearchType] = useState('title')
  const [books, setBooks] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [hasMore, setHasMore] = useState(false)
  const [page, setPage] = useState(1)
  const [hasSearched, setHasSearched] = useState(false)

  const handleSearch = async (searchQuery, searchTypeParam, pageNum = 1) => {
    if (!searchQuery.trim()) return

    setLoading(true)
    setError('')
    setHasSearched(true)

    try {
      const response = await searchBooks(searchQuery, searchTypeParam, pageNum)

      if (pageNum === 1) {
        setBooks(response.docs)
      } else {
        setBooks(prev => [...prev, ...response.docs])
      }

      setHasMore(response.docs.length === 20)
      setPage(pageNum)
    } catch (err) {
      setError('Failed to search books. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    handleSearch(query, searchType, 1)
  }

  const loadMore = () => {
    if (!loading && hasMore) {
      handleSearch(query, searchType, page + 1)
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder={`Search for books by ${searchType}...`}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={searchType}
            onChange={(e) => setSearchType(e.target.value)}
            className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="title">Title</option>
            <option value="author">Author</option>
            <option value="subject">Subject</option>
            <option value="isbn">ISBN</option>
          </select>
          <button
            type="submit"
            disabled={loading || !query.trim()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {loading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </form>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Results */}
      {books.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-900">
            Search Results ({books.length} books)
          </h2>

          {/* Books Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {books.map((book, index) => (
              <BookCard key={`${book.key}-${index}`} book={book} />
            ))}
          </div>

          {/* Load More Button */}
          {hasMore && (
            <div className="text-center">
              <button
                onClick={loadMore}
                disabled={loading}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Loading...' : 'Load More Books'}
              </button>
            </div>
          )}
        </div>
      )}

      {/* No Results */}
      {!loading && books.length === 0 && hasSearched && (
        <div className="text-center py-12">
          <p className="text-xl text-gray-600">No books found for "{query}"</p>
          <p className="text-gray-500 mt-2">Try a different search term or search type</p>
        </div>
      )}
    </div>
  )
}

// Book Card Component
const BookCard = ({ book }) => {
  const [imageError, setImageError] = useState(false)

  const coverUrl = book.cover_i
    ? `https://covers.openlibrary.org/b/id/${book.cover_i}-M.jpg`
    : null
  console.log(coverUrl);
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:scale-105 transition-all duration-300 group">
      {/* Book Cover */}
      <div className="aspect-[3/4] bg-gray-100 relative overflow-hidden">
        {!imageError && coverUrl ? (
          <img
            src={coverUrl}
            alt={`Cover of ${book.title}`}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 border-2 border-red-500"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 via-purple-50 to-indigo-100">
            <div className="text-center p-4">
              <div className="text-5xl mb-3 opacity-60">📚</div>
              <div className="text-xs text-gray-500 font-medium">No Cover Available</div>
            </div>
          </div>
        )}

        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
      </div>

      {/* Book Info */}
      <div className="p-5">
        <h3 className="font-bold text-gray-900 text-base leading-tight mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
          {book.title}
        </h3>

        {/* Authors */}
        {book.author_name && book.author_name.length > 0 && (
          <div className="flex items-center mb-3">
            <span className="text-xs text-gray-400 mr-2">👤</span>
            <p className="text-sm text-gray-600 line-clamp-1 font-medium">
              {book.author_name.slice(0, 2).join(', ')}
              {book.author_name.length > 2 && ` +${book.author_name.length - 2} more`}
            </p>
          </div>
        )}

        {/* Publication Year & Edition Count */}
        <div className="flex items-center justify-between mb-3">
          {book.first_publish_year && (
            <div className="flex items-center">
              <span className="text-xs text-gray-400 mr-1">📅</span>
              <span className="text-xs text-gray-600 font-medium">{book.first_publish_year}</span>
            </div>
          )}
          {book.edition_count && (
            <div className="flex items-center">
              <span className="text-xs text-gray-400 mr-1">📖</span>
              <span className="text-xs text-gray-600 font-medium">
                {book.edition_count} edition{book.edition_count !== 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>

        {/* Subjects */}
        {book.subject && book.subject.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {book.subject.slice(0, 2).map((subject, index) => (
              <span
                key={index}
                className="inline-block bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 text-xs px-3 py-1 rounded-full border border-blue-100 font-medium"
              >
                {subject.length > 15 ? subject.substring(0, 15) + '...' : subject}
              </span>
            ))}
            {book.subject.length > 2 && (
              <span className="inline-block bg-gray-50 text-gray-500 text-xs px-3 py-1 rounded-full border border-gray-200 font-medium">
                +{book.subject.length - 2}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default BookSearch
