import { useState } from 'react'
import { searchBooks } from '../services/openLibraryAPI'

const BookSearch = () => {
  const [query, setQuery] = useState('')
  const [books, setBooks] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [hasMore, setHasMore] = useState(false)
  const [page, setPage] = useState(1)

  const handleSearch = async (searchQuery, pageNum = 1) => {
    if (!searchQuery.trim()) return

    setLoading(true)
    setError('')

    try {
      const response = await searchBooks(searchQuery, pageNum)

      if (pageNum === 1) {
        setBooks(response.docs)
      } else {
        setBooks(prev => [...prev, ...response.docs])
      }

      setHasMore(response.docs.length === 20)
      setPage(pageNum)
    } catch (err) {
      setError('Failed to search books. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    handleSearch(query, 1)
  }

  const loadMore = () => {
    if (!loading && hasMore) {
      handleSearch(query, page + 1)
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex gap-4">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search for books by title..."
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            type="submit"
            disabled={loading || !query.trim()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </form>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Results */}
      {books.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-900">
            Search Results ({books.length} books)
          </h2>

          {/* Books Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {books.map((book, index) => (
              <BookCard key={`${book.key}-${index}`} book={book} />
            ))}
          </div>

          {/* Load More Button */}
          {hasMore && (
            <div className="text-center">
              <button
                onClick={loadMore}
                disabled={loading}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Loading...' : 'Load More Books'}
              </button>
            </div>
          )}
        </div>
      )}

      {/* No Results */}
      {!loading && books.length === 0 && query && (
        <div className="text-center py-12">
          <p className="text-xl text-gray-600">No books found for "{query}"</p>
          <p className="text-gray-500 mt-2">Try a different search term</p>
        </div>
      )}
    </div>
  )
}

// Book Card Component
const BookCard = ({ book }) => {
  const [imageError, setImageError] = useState(false)

  const coverUrl = book.cover_i
    ? `https://covers.openlibrary.org/b/id/${book.cover_i}-M.jpg`
    : null

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      {/* Book Cover */}
      <div className="aspect-[3/4] bg-gray-100 relative">
        {!imageError && coverUrl ? (
          <img
            src={coverUrl}
            alt={`Cover of ${book.title}`}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200">
            <div className="text-center p-4">
              <div className="text-4xl mb-2">📚</div>
              <div className="text-xs text-gray-600">No Cover</div>
            </div>
          </div>
        )}
      </div>

      {/* Book Info */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 text-sm leading-tight mb-2 line-clamp-2">
          {book.title}
        </h3>

        {/* Authors */}
        {book.author_name && book.author_name.length > 0 && (
          <p className="text-sm text-gray-600 mb-2 line-clamp-1">
            by {book.author_name.slice(0, 2).join(', ')}
            {book.author_name.length > 2 && ` +${book.author_name.length - 2} more`}
          </p>
        )}

        {/* Publication Year */}
        {book.first_publish_year && (
          <p className="text-xs text-gray-500 mb-2">
            Published: {book.first_publish_year}
          </p>
        )}

        {/* Edition Count */}
        {book.edition_count && (
          <p className="text-xs text-gray-500 mb-2">
            {book.edition_count} edition{book.edition_count !== 1 ? 's' : ''}
          </p>
        )}

        {/* Subjects */}
        {book.subject && book.subject.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {book.subject.slice(0, 2).map((subject, index) => (
              <span
                key={index}
                className="inline-block bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full"
              >
                {subject}
              </span>
            ))}
            {book.subject.length > 2 && (
              <span className="text-xs text-gray-500">
                +{book.subject.length - 2} more
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default BookSearch
