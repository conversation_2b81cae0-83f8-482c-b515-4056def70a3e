import React, { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Header from './components/Header'
import SearchPage from './components/SearchPage'
import BookDetails from './components/BookDetails'
import Favorites from './components/Favorites'
import './App.css'

const App = () => {
  const [favorites, setFavorites] = useState([])
  const [searchHistory, setSearchHistory] = useState([])

  // Load favorites and search history from localStorage on app start
  useEffect(() => {
    const savedFavorites = localStorage.getItem('bookFinder_favorites')
    const savedHistory = localStorage.getItem('bookFinder_searchHistory')

    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites))
    }

    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory))
    }
  }, [])

  // Save favorites to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('bookFinder_favorites', JSON.stringify(favorites))
  }, [favorites])

  // Save search history to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('bookFinder_searchHistory', JSON.stringify(searchHistory))
  }, [searchHistory])

  const addToFavorites = (book) => {
    setFavorites(prev => {
      const isAlreadyFavorite = prev.some(fav => fav.key === book.key)
      if (isAlreadyFavorite) {
        return prev.filter(fav => fav.key !== book.key)
      }
      return [...prev, book]
    })
  }

  const addToSearchHistory = (searchTerm, searchType = 'title') => {
    setSearchHistory(prev => {
      const newHistory = prev.filter(item =>
        !(item.term === searchTerm && item.type === searchType)
      )
      return [{ term: searchTerm, type: searchType, timestamp: Date.now() }, ...newHistory].slice(0, 10)
    })
  }

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Header favoritesCount={favorites.length} />
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route
              path="/"
              element={
                <SearchPage
                  favorites={favorites}
                  addToFavorites={addToFavorites}
                  searchHistory={searchHistory}
                  addToSearchHistory={addToSearchHistory}
                />
              }
            />
            <Route
              path="/book/:bookKey"
              element={
                <BookDetails
                  favorites={favorites}
                  addToFavorites={addToFavorites}
                />
              }
            />
            <Route
              path="/favorites"
              element={
                <Favorites
                  favorites={favorites}
                  addToFavorites={addToFavorites}
                />
              }
            />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App