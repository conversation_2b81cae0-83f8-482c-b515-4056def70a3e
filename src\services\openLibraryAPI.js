const BASE_URL = 'https://openlibrary.org'

// Search books using Open Library API
export const searchBooks = async (query, searchType = 'title', page = 1) => {
  try {
    const offset = (page - 1) * 20

    const url = new URL(`${BASE_URL}/search.json`)

    // Set search parameter based on search type
    switch (searchType) {
      case 'author':
        url.searchParams.append('author', query)
        break
      case 'subject':
        url.searchParams.append('subject', query)
        break
      case 'isbn':
        url.searchParams.append('isbn', query.replace(/[-\s]/g, ''))
        break
      case 'title':
      default:
        url.searchParams.append('title', query)
        break
    }

    url.searchParams.append('limit', '20')
    url.searchParams.append('offset', offset.toString())
    url.searchParams.append('fields', 'key,title,author_name,first_publish_year,cover_i,edition_count,subject')

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error searching books:', error)
    throw error
  }
}


