const BASE_URL = 'https://openlibrary.org'

// Search books using Open Library API
export const searchBooks = async (query, page = 1) => {
  try {
    const offset = (page - 1) * 20

    const url = new URL(`${BASE_URL}/search.json`)
    url.searchParams.append('title', query)
    url.searchParams.append('limit', '20')
    url.searchParams.append('offset', offset.toString())
    url.searchParams.append('fields', 'key,title,author_name,first_publish_year,cover_i,edition_count,subject')

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error searching books:', error)
    throw error
  }
}


