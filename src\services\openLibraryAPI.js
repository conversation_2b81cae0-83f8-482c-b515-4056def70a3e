const BASE_URL = 'https://openlibrary.org'

// Helper function to build search query
const buildSearchQuery = (searchParams) => {
  const { query, type, author, subject, publishYear, language } = searchParams
  let searchQuery = ''

  // Main search term
  switch (type) {
    case 'title':
      searchQuery = `title:"${query}"`
      break
    case 'author':
      searchQuery = `author:"${query}"`
      break
    case 'subject':
      searchQuery = `subject:"${query}"`
      break
    case 'isbn':
      searchQuery = `isbn:${query.replace(/[-\s]/g, '')}`
      break
    default:
      searchQuery = query
  }

  // Add advanced filters
  const filters = []
  if (author && type !== 'author') {
    filters.push(`author:"${author}"`)
  }
  if (subject && type !== 'subject') {
    filters.push(`subject:"${subject}"`)
  }
  if (publishYear) {
    filters.push(`first_publish_year:${publishYear}`)
  }
  if (language) {
    filters.push(`language:${language}`)
  }

  if (filters.length > 0) {
    searchQuery += ' AND ' + filters.join(' AND ')
  }

  return searchQuery
}

// Search books using Open Library API
export const searchBooks = async (searchParams, page = 1) => {
  try {
    const searchQuery = buildSearchQuery(searchParams)
    const offset = (page - 1) * 20
    
    const url = new URL(`${BASE_URL}/search.json`)
    url.searchParams.append('q', searchQuery)
    url.searchParams.append('limit', '20')
    url.searchParams.append('offset', offset.toString())
    url.searchParams.append('fields', 'key,title,author_name,author_key,first_publish_year,cover_i,cover_edition_key,edition_count,subject,publisher,language,isbn,ebook_access,ia')

    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    // Process and enhance the book data
    const processedBooks = data.docs.map(book => ({
      ...book,
      id: book.key,
      coverUrl: book.cover_i ? `https://covers.openlibrary.org/b/id/${book.cover_i}-M.jpg` : null,
      largeCoverUrl: book.cover_i ? `https://covers.openlibrary.org/b/id/${book.cover_i}-L.jpg` : null,
      authors: book.author_name || [],
      publishYear: book.first_publish_year,
      subjects: book.subject ? book.subject.slice(0, 5) : [], // Limit subjects for display
      publishers: book.publisher ? book.publisher.slice(0, 3) : [], // Limit publishers
      languages: book.language || [],
      isbns: book.isbn || [],
      hasEbook: book.ebook_access === 'borrowable' || book.ebook_access === 'public',
      internetArchive: book.ia || []
    }))

    return {
      ...data,
      docs: processedBooks
    }
  } catch (error) {
    console.error('Error searching books:', error)
    throw error
  }
}

// Get detailed book information
export const getBookDetails = async (bookKey) => {
  try {
    // Remove the '/works/' prefix if present
    const cleanKey = bookKey.replace('/works/', '')
    
    const response = await fetch(`${BASE_URL}/works/${cleanKey}.json`)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    // Get additional edition information
    let editionData = null
    try {
      const editionsResponse = await fetch(`${BASE_URL}/works/${cleanKey}/editions.json?limit=1`)
      if (editionsResponse.ok) {
        const editions = await editionsResponse.json()
        if (editions.entries && editions.entries.length > 0) {
          editionData = editions.entries[0]
        }
      }
    } catch (editionError) {
      console.warn('Could not fetch edition data:', editionError)
    }

    return {
      ...data,
      edition: editionData,
      description: data.description?.value || data.description || 'No description available',
      subjects: data.subjects || [],
      covers: data.covers || [],
      coverUrl: data.covers && data.covers.length > 0 
        ? `https://covers.openlibrary.org/b/id/${data.covers[0]}-L.jpg` 
        : null
    }
  } catch (error) {
    console.error('Error fetching book details:', error)
    throw error
  }
}

// Get author information
export const getAuthorInfo = async (authorKey) => {
  try {
    const cleanKey = authorKey.replace('/authors/', '')
    const response = await fetch(`${BASE_URL}/authors/${cleanKey}.json`)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    return {
      ...data,
      bio: data.bio?.value || data.bio || 'No biography available',
      photoUrl: data.photos && data.photos.length > 0 
        ? `https://covers.openlibrary.org/a/id/${data.photos[0]}-M.jpg` 
        : null
    }
  } catch (error) {
    console.error('Error fetching author info:', error)
    throw error
  }
}

// Get trending/popular books (using a predefined search for popular subjects)
export const getTrendingBooks = async () => {
  try {
    const popularSubjects = ['fiction', 'science fiction', 'mystery', 'romance', 'fantasy']
    const randomSubject = popularSubjects[Math.floor(Math.random() * popularSubjects.length)]
    
    const searchParams = {
      query: randomSubject,
      type: 'subject'
    }
    
    const results = await searchBooks(searchParams)
    return results.docs.slice(0, 12) // Return top 12 books
  } catch (error) {
    console.error('Error fetching trending books:', error)
    throw error
  }
}
