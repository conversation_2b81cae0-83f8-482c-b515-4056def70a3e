import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { ArrowLeft, Heart, Calendar, User, BookOpen, Download, ExternalLink, Globe } from 'lucide-react'
import { getBookDetails } from '../services/openLibraryAPI'
import LoadingSpinner from './LoadingSpinner'

const BookDetails = ({ favorites, addToFavorites }) => {
  const { bookKey } = useParams()
  const [book, setBook] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [imageError, setImageError] = useState(false)

  const isFavorite = book && favorites.some(fav => fav.key === book.key)

  useEffect(() => {
    const fetchBookDetails = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const bookData = await getBookDetails(bookKey)
        setBook(bookData)
      } catch (err) {
        setError('Failed to load book details. Please try again.')
        console.error('Error fetching book details:', err)
      } finally {
        setLoading(false)
      }
    }

    if (bookKey) {
      fetchBookDetails()
    }
  }, [bookKey])

  const handleToggleFavorite = () => {
    if (book) {
      // Create a simplified book object for favorites
      const favoriteBook = {
        key: book.key,
        title: book.title,
        author_name: book.authors || [],
        first_publish_year: book.created?.value ? new Date(book.created.value).getFullYear() : null,
        cover_i: book.covers?.[0],
        coverUrl: book.coverUrl
      }
      addToFavorites(favoriteBook)
    }
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-center py-12">
          <LoadingSpinner size="large" text="Loading book details..." />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <p className="text-red-700">{error}</p>
          <Link to="/" className="text-blue-600 hover:text-blue-700 mt-2 inline-block">
            ← Back to Search
          </Link>
        </div>
      </div>
    )
  }

  if (!book) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-12">
          <p className="text-gray-600 mb-4">Book not found</p>
          <Link to="/" className="text-blue-600 hover:text-blue-700">
            ← Back to Search
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Back Button */}
      <Link 
        to="/" 
        className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 mb-6 transition-colors"
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Search
      </Link>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="md:flex">
          {/* Book Cover */}
          <div className="md:w-1/3 lg:w-1/4">
            <div className="aspect-[3/4] bg-gray-100 relative">
              {!imageError && book.coverUrl ? (
                <img
                  src={book.coverUrl}
                  alt={`Cover of ${book.title}`}
                  className="w-full h-full object-cover"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200">
                  <BookOpen className="h-16 w-16 text-blue-400" />
                </div>
              )}
            </div>
          </div>

          {/* Book Information */}
          <div className="md:w-2/3 lg:w-3/4 p-6">
            <div className="flex justify-between items-start mb-4">
              <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                {book.title}
              </h1>
              <button
                onClick={handleToggleFavorite}
                className={`p-3 rounded-full transition-colors ${
                  isFavorite 
                    ? 'bg-red-500 text-white hover:bg-red-600' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-red-500'
                }`}
                title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
              >
                <Heart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />
              </button>
            </div>

            {/* Authors */}
            {book.authors && book.authors.length > 0 && (
              <div className="flex items-center gap-2 text-lg text-gray-700 mb-4">
                <User className="h-5 w-5" />
                <span>by {book.authors.join(', ')}</span>
              </div>
            )}

            {/* Publication Info */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
              {book.first_publish_year && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span>First published: {book.first_publish_year}</span>
                </div>
              )}
              
              {book.subjects && book.subjects.length > 0 && (
                <div className="flex items-center gap-2 text-gray-600">
                  <BookOpen className="h-4 w-4" />
                  <span>Subjects: {book.subjects.slice(0, 3).join(', ')}</span>
                </div>
              )}
            </div>

            {/* Description */}
            {book.description && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                <p className="text-gray-700 leading-relaxed">
                  {typeof book.description === 'string' 
                    ? book.description 
                    : book.description.value || 'No description available'}
                </p>
              </div>
            )}

            {/* Subjects */}
            {book.subjects && book.subjects.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Subjects</h3>
                <div className="flex flex-wrap gap-2">
                  {book.subjects.slice(0, 10).map((subject, index) => (
                    <span
                      key={index}
                      className="inline-block bg-blue-50 text-blue-700 text-sm px-3 py-1 rounded-full"
                    >
                      {subject}
                    </span>
                  ))}
                  {book.subjects.length > 10 && (
                    <span className="text-sm text-gray-500">
                      +{book.subjects.length - 10} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* External Links */}
            <div className="flex flex-wrap gap-3">
              <a
                href={`https://openlibrary.org${book.key}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Globe className="h-4 w-4" />
                View on Open Library
                <ExternalLink className="h-3 w-3" />
              </a>
              
              {book.edition && book.edition.isbn_13 && book.edition.isbn_13.length > 0 && (
                <a
                  href={`https://www.google.com/search?q=isbn:${book.edition.isbn_13[0]}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <BookOpen className="h-4 w-4" />
                  Find in Stores
                  <ExternalLink className="h-3 w-3" />
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookDetails
